.config-modal {
  .ant-modal-content {
    border-radius: 10px;
    background: #fff;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }

  .config-modal-layout {
    display: flex;
    flex-direction: row;
    height: 700px;
    min-width: 1200px;
    border-radius: 8px;
    overflow: hidden;
  }

  .config-sidebar {
    width: 280px;
    background: #fff;
    border-right: 1px dashed #9ca3af;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 16px;
    overflow-y: auto;

    .config-section {
      .config-title {
        font-size: 14px;
        color: #262628;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .chart-type-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-top: 8px;

        .chart-type-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          border: 1px solid #f0f0f0;
          height: 80px;
          padding: 8px;
          cursor: pointer;
          transition: border 0.2s, background 0.2s;

          .chart-image {
            width: 100%;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 6px;
            background: #fafafa;
            border-radius: 4px;

            img {
              width: 80%;
              height: 80%;
            }
          }

          .chart-name {
            font-size: 12px;
            color: #262628;
            text-align: center;
          }

          &:hover,
          &.selected {
            border: 1px solid #2868e7;
            background: #f8faff;
          }
        }
      }

      .ant-radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .ant-radio-wrapper {
          margin-right: 0;
        }
      }
    }
  }

  .config-center {
    width: 320px;
    background: #fff;
    border-right: 1px dashed #9ca3af;
    padding: 16px;
    display: flex;
    flex-direction: column;

    .config-title {
      font-size: 16px;
      color: #262628;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .settings-container {
      flex: 1;
      overflow-y: auto;
      padding-right: 8px;

      .theme-config {
        .ant-collapse {
          border: none;
          background: transparent;

          .ant-collapse-item {
            border: none;
            margin-bottom: 8px;

            .ant-collapse-header {
              padding: 8px 12px;
              background: #f6f8fa;
              border-radius: 6px;
              font-weight: 500;
              color: #262628;
            }

            .ant-collapse-content {
              border: none;
              background: transparent;

              .ant-collapse-content-box {
                padding: 12px 0;
              }
            }
          }
        }

        .config-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .ant-checkbox-wrapper,
          .sub-label {
            font-size: 14px;
            color: #333;
          }

          .ant-select,
          .ant-input-number,
          .ant-input {
            width: 120px;
          }
        }

        .sub-options {
          margin-left: 20px;
          margin-top: 8px;
          padding-left: 12px;
          border-left: 2px solid #f0f0f0;

          .config-item {
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  .config-preview {
    flex: 1;
    background: #f5f8fe url('/assets/jmpdmvqh.svg') repeat;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .preview-header {
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .preview-title {
        font-size: 18px;
        font-weight: 600;
        color: #222;
      }

      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }

    .preview-chart-area {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-preview-container {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        padding: 20px;

        .no-chart-selected {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;

          .placeholder-text {
            font-size: 16px;
            color: #999;
            text-align: center;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 1400px) {
  .config-modal {
    .config-modal-layout {
      min-width: 1000px;
    }

    .config-sidebar {
      width: 240px;
    }

    .config-center {
      width: 280px;
    }
  }
}

@media (max-width: 1200px) {
  .config-modal {
    .config-modal-layout {
      min-width: 900px;
      height: 600px;
    }

    .config-sidebar {
      width: 220px;
      gap: 16px;
    }

    .config-center {
      width: 260px;
    }
  }
}
