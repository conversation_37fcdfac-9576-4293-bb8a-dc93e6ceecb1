import React, { useState } from 'react';
import { Button, message } from 'antd';
import ConfigModal from './index';

/**
 * ConfigModal 使用示例
 */
const ConfigModalExample: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<any>(null);

  // 打开配置弹窗
  const handleOpenConfig = () => {
    setVisible(true);
  };

  // 关闭配置弹窗
  const handleCancel = () => {
    setVisible(false);
  };

  // 确认配置
  const handleConfirm = (config: any) => {
    console.log('配置结果:', config);
    setCurrentConfig(config);
    setVisible(false);
    message.success('配置保存成功');
  };

  // 编辑现有配置
  const handleEditConfig = () => {
    setVisible(true);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>配置弹窗示例</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <Button type="primary" onClick={handleOpenConfig} style={{ marginRight: '10px' }}>
          新建配置
        </Button>
        
        {currentConfig && (
          <Button onClick={handleEditConfig}>
            编辑配置
          </Button>
        )}
      </div>

      {currentConfig && (
        <div style={{ 
          background: '#f5f5f5', 
          padding: '16px', 
          borderRadius: '8px',
          marginTop: '20px'
        }}>
          <h3>当前配置:</h3>
          <pre style={{ 
            background: '#fff', 
            padding: '12px', 
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {JSON.stringify(currentConfig, null, 2)}
          </pre>
        </div>
      )}

      <ConfigModal
        visible={visible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        initialConfig={currentConfig}
      />
    </div>
  );
};

export default ConfigModalExample;
