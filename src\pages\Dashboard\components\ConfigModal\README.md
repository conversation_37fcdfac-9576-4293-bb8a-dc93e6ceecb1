# ConfigModal 配置弹窗组件

基于 AddChartModal 的设计风格，创建的通用配置弹窗组件。

## 功能特性

- 🎨 **三栏布局设计**：左侧配置区、中间参数设置区、右侧预览区
- 📊 **图表类型选择**：支持柱状图、折线图、饼图等多种图表类型
- ⚙️ **参数设置**：集成 ChartSettings 组件，支持主题、样式等配置
- 👀 **实时预览**：配置变更时实时更新图表预览
- 💾 **配置管理**：支持初始配置加载和配置重置

## 组件属性

```typescript
interface ConfigModalProps {
  visible: boolean;           // 是否显示弹窗
  onCancel: () => void;      // 取消回调
  onConfirm: (config: any) => void;  // 确认回调，返回配置对象
  initialConfig?: any;       // 初始配置（可选）
}
```

## 配置对象结构

```typescript
interface Config {
  dataType: string;      // 数据类型：'database' | 'excel' | 'api'
  chartType: string;     // 图表类型：'bar' | 'line' | 'pie' 等
  dimension: string;     // 维度字段
  valueField: string;    // 数值字段
  statType: number;      // 统计方式：0-统计记录总数，1-统计字段数值
  statMethod: string;    // 统计方法：'SUM' | 'MAX' | 'MIN' | 'AVERAGE' | 'COUNT'
  chartSettings: any;    // 图表样式设置
}
```

## 使用示例

### 基础用法

```tsx
import React, { useState } from 'react';
import { Button, message } from 'antd';
import ConfigModal from './components/ConfigModal';

const MyComponent = () => {
  const [visible, setVisible] = useState(false);

  const handleConfirm = (config: any) => {
    console.log('配置结果:', config);
    setVisible(false);
    message.success('配置保存成功');
  };

  return (
    <div>
      <Button type="primary" onClick={() => setVisible(true)}>
        打开配置
      </Button>
      
      <ConfigModal
        visible={visible}
        onCancel={() => setVisible(false)}
        onConfirm={handleConfirm}
      />
    </div>
  );
};
```

### 带初始配置的用法

```tsx
const initialConfig = {
  dataType: 'database',
  chartType: 'bar',
  dimension: 'date',
  valueField: 'amount',
  statType: 1,
  statMethod: 'SUM',
  chartSettings: {
    theme: 'theme1',
    showLegend: true,
    // ... 其他设置
  }
};

<ConfigModal
  visible={visible}
  onCancel={() => setVisible(false)}
  onConfirm={handleConfirm}
  initialConfig={initialConfig}
/>
```

## 布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                        配置弹窗                              │
├─────────────┬─────────────────┬─────────────────────────────┤
│   左侧配置区  │   中间参数设置区   │        右侧预览区          │
│             │                │                            │
│ • 数据类型   │   参数设置       │      图表预览              │
│ • 图表类型   │   ┌─────────┐   │   ┌─────────────────┐     │
│ • 维度字段   │   │ 主题设置 │   │   │                 │     │
│ • 统计方式   │   │ 样式配置 │   │   │   ECharts 图表   │     │
│ • 数值字段   │   │ 坐标轴   │   │   │                 │     │
│             │   │ 网格线   │   │   │                 │     │
│             │   └─────────┘   │   └─────────────────┘     │
│             │                │                            │
│             │                │   [重置] [确认]             │
└─────────────┴─────────────────┴─────────────────────────────┘
```

## 样式定制

组件使用 Less 样式文件，支持以下自定义：

- 弹窗尺寸：默认宽度 90%，高度 700px
- 三栏宽度：左侧 280px，中间 320px，右侧自适应
- 主题色彩：使用项目统一的 `#2868e7` 主色调
- 响应式：支持不同屏幕尺寸的适配

## 依赖组件

- `ChartSettings`：图表参数设置组件
- `ReactEcharts`：图表渲染组件
- `CHART_TYPES`：图表类型配置
- `applyChartSettings`：图表设置应用工具函数

## 注意事项

1. 确保项目中已安装并配置好 ECharts 相关依赖
2. ChartSettings 组件需要正确配置图表类型支持
3. 图表预览使用示例数据，实际使用时需要替换为真实数据
4. 配置对象的结构可根据实际需求进行调整

## 扩展建议

1. **数据源集成**：可以集成真实的数据源选择功能
2. **字段动态获取**：根据选择的数据源动态获取可用字段
3. **配置模板**：支持保存和加载配置模板
4. **验证增强**：添加更详细的配置验证逻辑
5. **国际化支持**：添加多语言支持
