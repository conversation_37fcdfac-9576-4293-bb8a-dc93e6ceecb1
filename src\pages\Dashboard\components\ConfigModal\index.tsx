import React, { useState, useEffect } from 'react';
import { Modal, Select, Button, Input, Radio, message, Collapse } from 'antd';
import './index.less';
import { CHART_TYPES, CHART_STYLE } from '@/config/charts';
import ChartSettings from '@/components/ChartSettings';
import ReactEcharts from 'echarts-for-react';
import { applyChartSettings } from '@/utils/chartSettings';

// 组件属性定义
interface ConfigModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (config: any) => void;
  initialConfig?: any;
}

// 数据类型选项
const DATA_TYPE_OPTIONS = [
  { value: 'database', label: '数据库' },
  { value: 'excel', label: 'Excel文件' },
  { value: 'api', label: 'API接口' },
];

// 统计方式选项
const STAT_TYPE_OPTIONS = [
  { value: 0, label: '统计记录总数' },
  { value: 1, label: '统计字段数值' },
];

// 统计方法选项
const STAT_METHOD_OPTIONS = [
  { value: 'MAX', label: '最大值' },
  { value: 'MIN', label: '最小值' },
  { value: 'SUM', label: '求和' },
  { value: 'AVERAGE', label: '平均值' },
  { value: 'COUNT', label: '计数' },
];

const ConfigModal: React.FC<ConfigModalProps> = ({ visible, onCancel, onConfirm, initialConfig = {} }) => {
  // 状态管理
  const [dataType, setDataType] = useState<string>('database');
  const [chartType, setChartType] = useState<string>('');
  const [dimension, setDimension] = useState<string>('');
  const [valueField, setValueField] = useState<string>('');
  const [statType, setStatType] = useState<number>(0);
  const [statMethod, setStatMethod] = useState<string>('SUM');
  const [chartSettings, setChartSettings] = useState<any>(CHART_STYLE);
  const [previewChartOption, setPreviewChartOption] = useState<any>(null);

  // 初始化配置
  useEffect(() => {
    if (visible && initialConfig) {
      setDataType(initialConfig.dataType || 'database');
      setChartType(initialConfig.chartType || '');
      setDimension(initialConfig.dimension || '');
      setValueField(initialConfig.valueField || '');
      setStatType(initialConfig.statType || 0);
      setStatMethod(initialConfig.statMethod || 'SUM');
      setChartSettings(initialConfig.chartSettings || CHART_STYLE);
    }
  }, [visible, initialConfig]);

  // 重置配置
  const resetConfig = () => {
    setDataType('database');
    setChartType('');
    setDimension('');
    setValueField('');
    setStatType(0);
    setStatMethod('SUM');
    setChartSettings(CHART_STYLE);
    setPreviewChartOption(null);
  };

  // 处理取消
  const handleCancel = () => {
    resetConfig();
    onCancel();
  };

  // 处理确认
  const handleConfirm = () => {
    // 验证必填项
    if (!chartType) {
      message.error('请选择图表类型');
      return;
    }

    if (!dimension) {
      message.error('请选择维度字段');
      return;
    }

    if (statType === 1 && !valueField) {
      message.error('统计字段数值时请选择数值字段');
      return;
    }

    const config = {
      dataType,
      chartType,
      dimension,
      valueField,
      statType,
      statMethod,
      chartSettings,
    };

    onConfirm(config);
    resetConfig();
  };

  // 处理图表设置变化
  const handleChartSettingsChange = (newSettings: any) => {
    setChartSettings(newSettings);
    // 实时更新预览图表
    updatePreviewChart(newSettings);
  };

  // 更新预览图表
  const updatePreviewChart = (settings?: any) => {
    if (!chartType) return;

    const chartTypeConfig = CHART_TYPES.find((chart) => chart.id === chartType);
    if (!chartTypeConfig) return;

    // 生成示例数据
    const sampleData = generateSampleData(chartType);

    // 应用图表设置
    const finalOption = applyChartSettings(sampleData, settings || chartSettings, chartType);
    setPreviewChartOption(finalOption);
  };

  // 生成示例数据
  const generateSampleData = (type: string) => {
    const sampleCategories = ['一月', '二月', '三月', '四月', '五月', '六月'];
    const sampleValues = [120, 200, 150, 80, 70, 110];

    switch (type) {
      case 'bar':
      case 'line':
        return {
          xAxis: {
            type: 'category',
            data: sampleCategories,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: '示例数据',
              type: type,
              data: sampleValues,
            },
          ],
        };
      case 'pie':
        return {
          series: [
            {
              type: 'pie',
              data: sampleCategories.map((name, index) => ({
                name,
                value: sampleValues[index],
              })),
            },
          ],
        };
      case 'horizontal-bar':
        return {
          yAxis: {
            type: 'category',
            data: sampleCategories,
          },
          xAxis: {
            type: 'value',
          },
          series: [
            {
              name: '示例数据',
              type: 'bar',
              data: sampleValues,
            },
          ],
        };
      default:
        return {
          xAxis: {
            type: 'category',
            data: sampleCategories,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: '示例数据',
              type: 'bar',
              data: sampleValues,
            },
          ],
        };
    }
  };

  // 获取预览图表配置
  const getPreviewChartOption = () => {
    return previewChartOption;
  };

  // 监听图表类型变化，更新预览
  useEffect(() => {
    if (chartType) {
      updatePreviewChart();
    }
  }, [chartType]);

  return (
    <Modal
      title="配置"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width="90%"
      className="config-modal common-modal"
      maskClosable={false}
      getContainer={false}
    >
      <div className="config-modal-layout">
        {/* 左侧配置区 */}
        <div className="config-sidebar">
          <div className="config-section">
            <div className="config-title">
              <span style={{ color: 'red', marginRight: 4 }}>*</span>数据类型
            </div>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择数据类型"
              value={dataType}
              onChange={setDataType}
              options={DATA_TYPE_OPTIONS}
            />
          </div>

          <div className="config-section">
            <div className="config-title">
              <span style={{ color: 'red', marginRight: 4 }}>*</span>图表类型
            </div>
            <div className="chart-type-grid">
              {CHART_TYPES.map((chart) => (
                <div
                  key={chart.id}
                  className={`chart-type-item${chartType === chart.id ? ' selected' : ''}`}
                  onClick={() => setChartType(chart.id)}
                >
                  <div className="chart-image">
                    <img src={chart.image} alt={chart.name} />
                  </div>
                  <div className="chart-name">{chart.name}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="config-section">
            <div className="config-title">
              <span style={{ color: 'red', marginRight: 4 }}>*</span>维度
            </div>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择维度字段"
              value={dimension}
              onChange={setDimension}
              options={[
                { value: 'date', label: '日期' },
                { value: 'category', label: '分类' },
                { value: 'region', label: '地区' },
              ]}
            />
          </div>

          <div className="config-section">
            <div className="config-title">
              <span style={{ color: 'red', marginRight: 4 }}>*</span>统计方式
            </div>
            <Radio.Group value={statType} onChange={(e) => setStatType(e.target.value)} options={STAT_TYPE_OPTIONS} />
          </div>

          {statType === 1 && (
            <div className="config-section">
              <div className="config-title">
                <span style={{ color: 'red', marginRight: 4 }}>*</span>数值
              </div>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择数值字段"
                value={valueField}
                onChange={setValueField}
                options={[
                  { value: 'amount', label: '金额' },
                  { value: 'count', label: '数量' },
                  { value: 'score', label: '分数' },
                ]}
              />
              <div style={{ marginTop: 8 }}>
                <Select
                  style={{ width: '100%' }}
                  placeholder="请选择统计方法"
                  value={statMethod}
                  onChange={setStatMethod}
                  options={STAT_METHOD_OPTIONS}
                />
              </div>
            </div>
          )}
        </div>

        {/* 中间参数设置区 */}
        <div className="config-center">
          <div className="config-title">参数设置</div>
          <div className="settings-container">
            <ChartSettings
              onChange={handleChartSettingsChange}
              defaultValues={chartSettings}
              chartType={chartType as any}
              layout="vertical"
            />
          </div>
        </div>

        {/* 右侧预览区 */}
        <div className="config-preview">
          <div className="preview-header">
            <div className="preview-title">预览</div>
            <div className="preview-actions">
              <Button style={{ marginRight: 8 }} onClick={resetConfig}>
                重置
              </Button>
              <Button type="primary" onClick={handleConfirm}>
                确认
              </Button>
            </div>
          </div>
          <div className="preview-chart-area">
            <div className="chart-preview-container">
              {chartType ? (
                <ReactEcharts
                  key={chartType}
                  option={getPreviewChartOption()}
                  style={{ height: '100%', width: '100%' }}
                  opts={{ renderer: 'canvas' }}
                />
              ) : (
                <div className="no-chart-selected">
                  <div className="placeholder-text">请选择图表类型</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ConfigModal;
